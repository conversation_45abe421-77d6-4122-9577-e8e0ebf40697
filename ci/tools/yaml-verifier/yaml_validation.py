"""
This script validates a list of YAML files for syntax and content before merging.
"""
from typing import List
import sys
import yaml
from strictyaml import load, Map, Seq, Str, Regex,YAMLValidationError

# Define file paths as a list of constants for easy management
blacklist_schema = Map({"blacklist-remote-write": Map({"regex": Seq(Regex(u"[a-zA-Z0-9_:\.\*]*"))})})
whitelist_schema = Map({"kubernetes-pods": Map({"regex": Seq(Regex(u"[a-zA-Z0-9_:\.\*]*"))})})
prometheus_schema = None
schemas = {
    'configengine/charts/tenant/prometheus/files/blacklist.yaml': blacklist_schema,
    'configengine/charts/tenant/prometheus/files/whitelist.yaml': whitelist_schema,
    'configengine/charts/engine/prometheus/files/prometheus.yaml': prometheus_schema,
}
YAML_FILE_PATHS = [
    'configengine/charts/tenant/prometheus/files/prometheus.rules',
    'configengine/charts/tenant/prometheus/files/whitelist.yaml',
    'configengine/charts/tenant/prometheus/files/blacklist.yaml',
]

def validate_schemas() -> bool:
    for file_path, schema in schemas.items():
        print(f"Validating {file_path} against schema {schema}")
        if schema is None:
            return True
        with open(file_path, 'r') as f:
            try:
                load(f.read(), schema).data
            except YAMLValidationError as e:
                print(f"❌ Validation FAILED for '{file_path}': Error parsing YAML.")
                print(f"   Details: {e}")
                return False
    return True






def main() -> None:
    """
    Main function to loop through and validate all specified YAML files.
    Exits with a non-zero status code if any validation fails.
    """
    print("--- Starting YAML Validation ---")
    # Use a list comprehension to run validation on all files and store results
    validation_results = validate_schemas()
    if validation_results:
        print("🎉 All YAML files are valid.")
        sys.exit(0)
    else:
        print("\n🚫 One or more YAML files failed validation. Please review the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
