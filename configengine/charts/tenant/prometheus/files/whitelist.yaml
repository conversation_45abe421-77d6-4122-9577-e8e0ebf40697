kubernetes-pods:
  regex:
    - active_cloud_connectors
    - active_cloud_outposts
    - agent_mgmt_processor_pubsub_consumer_errors_total
    - agent_mgmt_processor_pubsub_consumer_inflight_messages
    - agent_mgmt_processor_pubsub_consumer_latency_seconds
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_count
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_sum
    - agent_mgmt_processor_pubsub_consumer_messages_total  
    - agent_to_group_calculation_count
    - agent_to_group_calculation_duration
    - alert_monitoring_metrics_bigquery_alert_status_counts
    - alert_monitoring_metrics_mysql_alert_status_counts
    - alert_monitoring_metrics_close_incident_with_open_alert_counts
    - alert_monitoring_metrics_open_alerts_per_incident_counts
    - alert_monitoring_metrics_alert_internal_ids_per_external_id
    - alert_monitoring_metrics_mysql_incident_status_counts
    - alert_monitoring_metrics_bigquery_incident_status_counts
    - alert_monitoring_metrics_duplicate_alert_counts
    - alert_monitoring_metrics_incident_alert_count_mismatch
    - alert_monitoring_metrics_incident_with_no_alert_counts
    - alert_monitoring_metrics_open_alert_older_than_10_days_counts
    - analytics__count_events_that_were_fetched_from_bq_total
    - analytics__count_events_that_were_fetched_from_gcs_total
    - analytics__tenant_have_reached_enrichment_limit
    - analytics_active_hosts
    - analytics_content_delta_between_requested_sync_to_sync_finished
    - analytics_content_delta_time_from_insertion
    - analytics_correlations_.+
    - analytics_cycle_running
    - analytics_de_v2__batches_processed_wall_time_count
    - analytics_de_v2__batches_processed_wall_time_sum
    - analytics_de_v2__events_processed_count
    - analytics_de_v2__events_processed_per_type_sum
    - analytics_de_v2__events_processed_sum
    - analytics_de_v2_detection_component_wall_time_sum
    - analytics_de_v2_detection_processing_part_wall_time_sum
    - analytics_de_v2_internal_queue_size
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total
    - analytics_de_v2_profile_engine_api_keys_count_sum
    - analytics_de_v2_profile_engine_api_request_time_sum
    - analytics_de_v2_rocks_keys_count_count
    - analytics_de_v2_rocks_keys_count_sum
    - analytics_de_v2_rocks_request_time_count
    - analytics_de_v2_rocks_request_time_sum
    - analytics_de_v2_vectorized_matcher_compile_detectors_sum
    - analytics_de_v2_vectorized_matcher_layer_wall_time_sum
    - analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total
    - analytics_de_v2_vectorized_matcher_wall_time_sum
    - analytics_decay_time_per_staging_table_count
    - analytics_decay_time_per_staging_table_sum
    - analytics_decision_time_per_staging_table_count
    - analytics_decision_time_per_staging_table_sum
    - analytics_delta_time_from_last_successful_decider_calculation
    - analytics_detection_component_process_time_count
    - analytics_detection_component_process_time_sum
    - analytics_detection_emitted_alerts_count
    - analytics_detection_emitted_alerts_sum
    - analytics_detection_engine_consumer_nacks_total
    - analytics_detection_hit_publish_time_count
    - analytics_detection_hit_publish_time_sum
    - analytics_detection_num_of_analytics_product_access_total
    - analytics_detection_outer_udf_execution_time_count
    - analytics_detection_outer_udf_execution_time_sum
    - analytics_detection_profile_matcher_get_profile_from_db_time_count
    - analytics_detection_profile_matcher_get_profile_from_db_time_sum
    - analytics_detection_state_populator_ingestion_rows_total
    - analytics_detection_state_populator_ingestion_time_count
    - analytics_detection_state_populator_ingestion_time_sum
    - analytics_detection_udf_execution_time_count
    - analytics_detection_udf_execution_time_sum
    - analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time
    - analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time
    - analytics_dss_last_updated_on_last_change
    - analytics_dss_sync_times_difference
    - analytics_dynamic_profile_updater_time_count
    - analytics_dynamic_profile_updater_time_sum
    - analytics_enabled
    - analytics_flood_stuck_alerts_total
    - analytics_flood_tasks_total
    - analytics_global_flood_total
    - analytics_global_profiles_contribution_failure
    - analytics_installed_content_version
    - analytics_llm_max_daily_requests_reached
    - analytics_llm_requests_bucket
    - analytics_llm_requests_count
    - analytics_llm_requests_sum
    - analytics_llm_task_success_rate_total
    - analytics_llm_task_success_rate_created
    - analytics_local_flood_total
    - analytics_objects_table_last_creation_time
    - analytics_product_consecutive_stage_failures
    - analytics_product_last_successful_api_table_creation_timestamp
    - analytics_profile_.+
    - analytics_rocksdb_num_keys_query
    - analytics_rocksdb_num_requests
    - analytics_rocksdb_response_time_count
    - analytics_rocksdb_response_time_sum
    - analytics_rocksdb_backup_download_failed
    - analytics_rocksdb_compaction_tasks
    - analytics_role_calculation_delay
    - analytics_role_count
    - analytics_task_processor_parse_metablob_error_created
    - analytics_task_processor_parse_metablob_error_total
    - analytics_task_processor_process_message_time_count
    - apisec_asset_manager_asset_manager_delete_assets_total_errors
    - apisec_asset_manager_asset_manager_get_assets_retention_total_errors
    - apisec_asset_manager_asset_manager_get_assets_total_errors
    - apisec_asset_manager_asset_manager_publish_assets_total_errors
    - apisec_asset_manager_assets_batch_size
    - apisec_asset_manager_assets_retention_flow_duration_seconds_bucket
    - apisec_asset_manager_assets_retention_flow_duration_seconds_sum
    - apisec_asset_manager_assets_retention_flow_duration_seconds_count
    - apisec_asset_manager_delete_asset_messages_total
    - apisec_asset_manager_get_assets_status_code
    - apisec_asset_manager_process_specs_counter
    - apisec_asset_manager_publish_uai_flow_duration_seconds_bucket
    - apisec_asset_manager_publish_uai_flow_duration_seconds_count
    - apisec_asset_manager_publish_uai_flow_duration_seconds_sum
    - apisec_asset_manager_pubsub_input_total_errors
    - apisec_asset_manager_total_assets_ETL_timeouts
    - apisec_asset_manager_total_delete_asset_messages
    - apisec_asset_manager_total_number_of_input_DTOs
    - apisec_asset_manager_total_number_of_pubsub_input_messages
    - apisec_asset_manager_total_publish_asset_messages
    - apisec_asset_manager_total_retries_for_get_assets_api_call
    - apisec_asset_manager_total_UAI_asset_ingestion_errors
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_count
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_sum
    - apisec_bff_api_concurrent_requests
    - apisec_bff_api_latency_seconds_bucket
    - apisec_bff_api_request_size_bytes_bucket
    - apisec_bff_api_requests_total
    - apisec_bff_api_response_size_bytes_bucket
    - apisec_enricher_apigw_handle_metablob_time_ms
    - apisec_enricher_apigw_http_transactions_creation_count
    - apisec_enricher_apigw_http_transactions_creation_error_count
    - apisec_enricher_apigw_http_transactions_creation_error_on_second_time_count
    - apisec_enricher_apigw_http_transactions_creation_on_second_time_count
    - apisec_enricher_classification_time_seconds_bucket
    - apisec_enricher_classification_time_seconds_count
    - apisec_enricher_classification_time_seconds_sum
    - apisec_enricher_classification_total
    - apisec_enricher_classification_error_total
    - apisec_enricher_find_geo_ip_location_time_ms
    - apisec_enricher_http_handler_handle_metablob_time_ms
    - apisec_enricher_metablob_handler_not_found_count
    - apisec_enricher_metablob_parsing_errors_count
    - apisec_enricher_parsing_rules_raw_data_into_dto_errors_count
    - apisec_enricher_pubsub_callback_time_ms
    - apisec_enricher_received_pubsub_msgs_count
    - apisec_enricher_send_http_transactions_to_groupping_pubsub_count
    - apisec_enricher_set_module_sources_errors_count
    - apisec_grouping_service_api_model_collapse_per_depth_per_height
    - apisec_grouping_service_api_tree_check_tree_logic_duration_bucket
    - apisec_grouping_service_api_tree_check_tree_logic_duration_count
    - apisec_grouping_service_api_tree_check_tree_logic_duration_sum
    - apisec_grouping_service_api_tree_number_of_trees_per_height
    - apisec_grouping_service_mongo_operation_latency_seconds_bucket
    - apisec_grouping_service_mongo_operation_latency_seconds_count
    - apisec_grouping_service_mongo_operation_latency_seconds_sum
    - apisec_grouping_service_number_of_filtered_paths_by_filter_type
    - apisec_grouping_service_number_of_paths_per_onboarding_status
    - apisec_grouping_service_pubsub_input_counter
    - apisec_grouping_service_pubsub_input_error_counter
    - apisec_grouping_service_pubsub_output_counter
    - apisec_grouping_service_pubsub_output_error_counter
    - apisec_grouping_service_regex_check_regex_duration_bucket
    - apisec_grouping_service_regex_check_regex_duration_count
    - apisec_grouping_service_regex_check_regex_duration_sum
    - apisec_grouping_service_regex_hits_total
    - apisec_grouping_service_single_transaction_flow_duration_bucket
    - apisec_grouping_service_single_transaction_flow_duration_count
    - apisec_grouping_service_single_transaction_flow_duration_sum
    - apisec_inspection_api_aggregation_counter
    - apisec_inspection_api_aggregation_duration_bucket
    - apisec_inspection_api_aggregation_duration_count
    - apisec_inspection_api_aggregation_duration_sum
    - apisec_inspection_api_lru_init_counter
    - apisec_inspection_bit_aggregation_counter
    - apisec_inspection_content_error
    - apisec_inspection_content_pull
    - apisec_inspection_content_pull_duration_bucket
    - apisec_inspection_content_pull_duration_count
    - apisec_inspection_content_pull_duration_sum
    - apisec_inspection_content_update_check
    - apisec_inspection_content_update_check_duration_bucket
    - apisec_inspection_content_update_check_duration_count
    - apisec_inspection_content_update_check_duration_sum
    - apisec_inspection_content_update_check_error
    - apisec_inspection_issue_count
    - apisec_inspection_issue_creation_error_count
    - apisec_inspection_lru_entry_total_counter
    - apisec_inspection_lru_error
    - apisec_inspection_malformed_content
    - apisec_inspection_message_processing_duration_bucket
    - apisec_inspection_message_processing_duration_count
    - apisec_inspection_message_processing_duration_sum
    - apisec_inspection_pubsub_input_counter
    - apisec_inspection_pubsub_input_error_counter
    - apisec_inspection_pubsub_output_counter
    - apisec_inspection_pubsub_output_error_counter
    - apisec_inspection_rule_engine_aggregation_cache_eviction
    - apisec_inspection_rule_engine_cycle
    - apisec_inspection_rule_engine_execution
    - apisec_inspection_rule_error
    - apisec_inspection_rule_evaluation_duration_bucket
    - apisec_inspection_rule_execution_duration_bucket
    - apisec_inspection_telemetry_error_counter
    - apisec_inspection_telemetry_sent_counter
    - apisec_inspection_telemetry_throttled_counter
    - apisec_inspection_unknown_content
    - apisec_issue_patcher_cron_job_errors_total
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_bucket
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_count
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_sum
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_bucket
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_count
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_sum
    - apisec_issue_patcher_cron_job_issues_total
    - apisec_risk_engine_content_errors_total
    - apisec_risk_engine_drift_detection_duration_seconds_bucket
    - apisec_risk_engine_drift_detection_duration_seconds_count
    - apisec_risk_engine_drift_detection_duration_seconds_sum
    - apisec_risk_engine_dspm_classification_call_duration_in_seconds_bucket
    - apisec_risk_engine_dspm_load_errors_total
    - apisec_risk_engine_get_speclets_calls_total
    - apisec_risk_engine_get_speclets_duration_seconds_bucket
    - apisec_risk_engine_get_speclets_duration_seconds_count
    - apisec_risk_engine_get_speclets_duration_seconds_sum
    - apisec_risk_engine_get_speclets_invalid_total
    - apisec_risk_engine_get_spec_calls_total
    - apisec_risk_engine_get_spec_duration_seconds_bucket
    - apisec_risk_engine_get_spec_duration_seconds_count
    - apisec_risk_engine_get_spec_duration_seconds_sum
    - apisec_risk_engine_findings_upsert_duration_seconds_bucket
    - apisec_risk_engine_findings_upsert_duration_seconds_count
    - apisec_risk_engine_findings_upsert_duration_seconds_sum
    - apisec_risk_engine_findings_upsert_emits
    - apisec_risk_engine_findings_upserted
    - apisec_risk_engine_issues_upsert_duration_seconds_bucket
    - apisec_risk_engine_issues_upsert_duration_seconds_count
    - apisec_risk_engine_issues_upsert_duration_seconds_sum
    - apisec_risk_engine_issues_upsert_emits
    - apisec_risk_engine_issues_upserted
    - apisec_risk_engine_metablobs_handled
    - apisec_risk_engine_specs_handled_total
    - apisec_risk_engine_spec_cache_access_total
    - apisec_risk_engine_spec_risks_detection_duration_seconds_bucket
    - apisec_risk_engine_spec_risks_detection_duration_seconds_count
    - apisec_risk_engine_spec_risks_detection_duration_seconds_sum
    - apisec_risk_engine_spec_static_scan_failures_total
    - apisec_risk_engine_transaction_handling_duration_in_seconds_bucket
    - apisec_risk_engine_transaction_handling_duration_in_seconds_count
    - apisec_risk_engine_transaction_handling_duration_in_seconds_sum
    - apisec_risk_engine_transactions_handled
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum
    - apisec_scan_manager_upload_scan_results_error_total
    - apisec_scan_manager_upload_scan_results_count_total
    - apisec_spec_service_cron_spec_gate_duration_seconds_sum
    - apisec_spec_service_cron_spec_gate_duration_seconds_count
    - apisec_spec_service_cron_spec_gate_duration_seconds_bucket
    - apisec_spec_service_errors_total
    - apisec_spec_service_file_size_bytes_sum
    - apisec_spec_service_file_size_bytes_count
    - apisec_spec_service_file_size_bytes_bucket
    - apisec_spec_service_mongo_duration_seconds_bucket
    - apisec_spec_service_mongo_duration_seconds_count
    - apisec_spec_service_mongo_duration_seconds_sum
    - apisec_spec_service_pubsub_duration_seconds_bucket
    - apisec_spec_service_pubsub_duration_seconds_count
    - apisec_spec_service_pubsub_duration_seconds_sum
    - apisec_spec_service_records_fetched_total
    - apisec_spec_service_records_handled_total
    - app_hub_prometheus_ingester_edr_errors_total
    - app_hub_prometheus_ingester_xql_errors_total
    - application_hub_permanent_errors_total
    - application_hub_public_api_requests_total
    - application_hub_temporary_errors_total
    - asm_alerts_mitre_backfill_sync_error_total
    - asm_etl_complete
    - asm_etl_count
    - asm_etl_duration
    - asm_export_assets_gauge
    - asm_incidents_mitre_backfill_sync_error_total
    - asm_mitre_mappings_sync_error_total
    - asset_mgmt_assoc_engine_acquire_lock_count_total
    - asset_mgmt_assoc_engine_association_conflicts_count
    - asset_mgmt_assoc_engine_association_process_time_count
    - asset_mgmt_assoc_engine_association_process_time_sum
    - asset_mgmt_diff_maker_last_exec_time_sec
    - asset_mgmt_general_assets_count_per_cloud_provider
    - asset_mgmt_general_assets_count_per_source
    - asset_mgmt_general_total_assets_count
    - asset_mgmt_ingester_assets_processed_count_total
    - asset_mgmt_reducer_last_exec_time_sec
    - asset_mgmt_snapshot_mgr_acquire_lock_total
    - archive_storage_aggregator_aggregator_compression_rate
    - archive_storage_aggregator_aggregator_process_duration
    - archive_storage_aggregator_committed_object_size
    - archive_storage_aggregator_compression_job_status
    - archive_storage_aggregator_delete_objects_count
    - archive_storage_aggregator_parse_error_count
    - archive_storage_aggregator_process_object_duration_micro_seconds_bucket
    - archive_storage_aggregator_processed_bytes_total
    - archive_storage_aggregator_processed_objects_count
    - archive_storage_aggregator_raw_object_size
    - argo_workflows_.+
    - attack_path_rules
    - attack_path_start_total
    - attack_path_success_total
    - attack_path_verdicts
    - attack_path_failure
    - auto_suggest_failure_total
    - batch_scanner_assets
    - batch_scanner_assets_scanned_milliseconds_bucket
    - batch_scanner_assets_scanned_milliseconds_count
    - batch_scanner_assets_scanned_milliseconds_sum
    - batch_scanner_rules_processed_total
    - batch_scanner_verdict_generated
    - batch_scanner_scanlog_generated
    - batch_scanner_scanlog_export_timer_milliseconds_sum
    - batch_scanner_scanlog_page_exported_total
    - batch_scanner_scanlog_exported_size_kb_sum
    - bigquery_adapter_.+
    - cas_applications_job_application_functions_seconds_bucket
    - cas_applications_job_application_functions_seconds_count
    - cas_applications_job_application_functions_seconds_sum
    - cas_dashboards_api_service_duration_seconds_bucket
    - cas_dashboards_api_service_duration_seconds_count
    - cas_dashboards_api_service_duration_seconds_sum
    - cas_product_analytics_send_to_cortex_seconds_bucket
    - cas_product_analytics_send_to_cortex_seconds_count
    - cas_product_analytics_send_to_cortex_seconds_sum
    - cas_persistence_app_genesis_errors_total
    - cas_persistence_app_lens_errors_total
    - cas_persistence_app_stream_errors_total
    - cas_persistence_app_code_errors_total
    - cas_persistence_scai_errors_total
    - cas_persistence_scan_ops_errors_total
    - cas_persistence_core_errors_total
    - cas_persistence_flow_errors_total
    - cas_persistence_issues_errors_total
    - cas_persistence_issues_upsert_total
    - cas_persistence_lilo_errors_total
    - cas_persistence_stitch_errors_total
    - cas_http_request_duration_seconds_bucket
    - cas_http_request_duration_seconds_count
    - cas_http_request_duration_seconds_sum
    - cas_http_request_size_bytes_count
    - cas_http_request_size_bytes_sum
    - cas_http_response_size_bytes_count
    - cas_http_response_size_bytes_sum
    - cas_job_duration_seconds_bucket
    - cas_job_duration_seconds_count
    - cas_job_duration_seconds_sum
    - cas_job_artifacts_size_bytes_count
    - cas_job_artifacts_size_bytes_sum
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_bucket
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_count
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_sum
    - cas_unified_cli_command_count_total
    - cas_unified_cli_os_total
    - cas_unified_cli_scan_result_total
    - ciem_counter_collection_list_accounts_api_results_counter_total
    - ciem_counter_epc_validation_snapshot_failure_counter_total
    - ciem_counter_epc_sync_page_upload_failure_counter_total
    - ciem_counter_epc_time_to_permission_kpi_total
    - ciem_counter_excessive_evidence_cache_hit_total
    - ciem_counter_excessive_evidence_cache_miss_total
    - ciem_counter_excessive_evidence_cache_unexpected_miss_total
    - ciem_counter_excessive_evidence_calculation_error_total
    - ciem_counter_excessive_evidence_inline_policy_service_error_total
    - ciem_counter_excessive_evidence_issue_failure_total
    - ciem_counter_excessive_evidence_issue_success_total
    - ciem_counter_excessive_evidence_persistent_storage_hits_total
    - ciem_counter_excessive_evidence_provider_error_total
    - ciem_counter_issues_publisher_total_published_total
    - ciem_counter_last_access_processor_added_records_counter_total
    - ciem_counter_last_access_audit_log_transformer_processed_records_counter
    - ciem_counter_last_access_audit_log_transformer_processed_records_counter_total
    - ciem_counter_last_access_audit_log_transformer_unrecognized_records_counter_total
    - ciem_counter_last_access_empty_pages_total
    - ciem_counter_last_access_processor_compacted_records_counter_total
    - ciem_counter_last_access_processor_merged_supported_actions_counter_total
    - ciem_counter_least_privileged_access_too_many_policies_total
    - ciem_counter_sync_sync_publish_accounts_counter_total
    - ciem_gauge_account_manager_list_accounts_api_result_size
    - ciem_gauge_collection_list_accounts_api_results_gauge
    - ciem_gauge_collection_accounts_by_cloud_type
    - ciem_gauge_creation_time_slot_time_sec
    - ciem_gauge_creation_time_total_bytes_processed
    - ciem_gauge_epc_snapshot_status_account_monitor
    - ciem_gauge_epc_status_account_monitor
    - ciem_gauge_epc_sync_permissions_calculated_gauge
    - ciem_gauge_epc_validation_snapshot_to_big_query_time
    - ciem_gauge_issues_publisher_total_published
    - ciem_gauge_last_access_processor_supported_actions_gauge
    - ciem_gauge_metrics_publisher_human_identity_with_admin_permission
    - ciem_gauge_metrics_publisher_non_human_identity_with_admin_permission
    - ciem_gauge_metrics_publisher_new_issues
    - ciem_gauge_metrics_publisher_resolved_issues
    - ciem_gauge_metrics_publisher_excessive_policies
    - ciem_gauge_metrics_publisher_assumed_by_third_party
    - ciem_gauge_metrics_publisher_assumed_by_third_party_with_admin_permission
    - ciem_gauge_metrics_publisher_total_issues
    - ciem_gauge_metrics_publisher_unused_permissions
    - ciem_gauge_metrics_publisher_identity_category_permissions
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_numIssues
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_avgDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_maxDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_minDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_avgSinceCalculated
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_maxSinceCalculated
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_minSinceCalculated
    - ciem_gauge_rule_scanner_orchestrator_success_percentages
    - ciem_gauge_rule_scanner_orchestrator_total_rules
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_max
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_count
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_sum
    - ciem_timer_api_controller_elapsed_time_seconds_count
    - ciem_timer_api_controller_elapsed_time_seconds_max
    - ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_count
    - ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_sum
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_count
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_sum
    - ciem_timer_collection_list_accounts_api_time_seconds_count
    - ciem_timer_creation_time_maintenance_job_elapsed_time_seconds_max
    - ciem_timer_epc_consumer_total_time_seconds_max
    - ciem_timer_epc_consumer_total_time_seconds_count
    - ciem_timer_epc_consumer_total_time_seconds_sum
    - ciem_timer_epc_sync_total_sync_time_seconds_count
    - ciem_timer_epc_sync_total_sync_time_seconds_sum
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_max
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_count
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_max
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_sum
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_count
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_max
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_sum
    - ciem_timer_graph_controller_calculate_graph_seconds_count
    - ciem_timer_graph_controller_calculate_graph_seconds_max
    - ciem_timer_graph_controller_calculate_graph_seconds_sum
    - ciem_timer_job_elapsed_time_seconds_count
    - ciem_timer_job_elapsed_time_seconds_max
    - ciem_timer_job_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_max
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_max
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_count
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_max
    - ciem_timer_last_access_processor_job_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_job_elapsed_time_seconds_max
    - ciem_timer_least_privileged_access_controller_metadata_seconds_count
    - ciem_timer_least_privileged_access_controller_metadata_seconds_max
    - ciem_timer_least_privileged_access_controller_metadata_seconds_sum
    - ciem_timer_least_privileged_access_controller_optimize_seconds_count
    - ciem_timer_least_privileged_access_controller_optimize_seconds_max
    - ciem_timer_least_privileged_access_controller_optimize_seconds_sum
    - ciem_timer_rule_management_get_enabled_rules_seconds_count
    - ciem_timer_rule_management_get_enabled_rules_seconds_max
    - ciem_timer_rule_management_get_enabled_rules_seconds_sum
    - ciem_timer_rule_scanner_orchestrator_run_seconds_sum
    - ciem_timer_rule_scanner_orchestrator_run_seconds_count
    - ciem_timer_rule_scanner_orchestrator_run_seconds_max
    - ciem_timer_rule_scanner_run_seconds_count
    - ciem_timer_rule_scanner_run_seconds_max
    - ciem_timer_rule_scanner_run_seconds_sum
    - ciem_timer_sync_total_time_seconds_count
    - ciem_timer_sync_total_time_seconds_max
    - ciem_timer_sync_total_time_seconds_sum
    - ciem_timer_table_controller_get_data_seconds_count
    - ciem_timer_table_controller_get_data_seconds_max
    - ciem_timer_table_controller_get_data_seconds_sum
    - ciem_timer_table_controller_get_view_def_seconds_count
    - ciem_timer_table_controller_get_view_def_seconds_max
    - ciem_timer_table_controller_get_view_def_seconds_sum
    - ciem_timer_epc_phase_total_time_seconds_max
    - ciem_timer_epc_phase_total_time_seconds_count
    - ciem_timer_epc_phase_total_time_seconds_sum
    - ciem_gauge_epc_validation_time_passed_since_last_snapshot
    - ciem_counter_health_check_execution_total
    - ciem_gauge_static_evidence_issue_batch_processor_success_percentage
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_count
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_max
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_sum
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_count
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_sum
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_max
    - ciem_timer_static_evidence_processor_run_total_time_seconds_count
    - ciem_timer_static_evidence_processor_run_total_time_seconds_sum
    - ciem_timer_static_evidence_processor_run_total_time_seconds_max
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_count
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_sum
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_max
    - ciem_counter_static_evidence_single_issue_sync_counter
    - cloud_connectors_templates_created_total
    - cloud_onboarding_errors_total
    - cloud_outposts_templates_created_total
    - cloud_assets_collection_csp_api_request_duration_seconds_bucket
    - cloud_assets_collection_csp_api_request_duration_seconds_count
    - cloud_assets_collection_csp_api_request_duration_seconds_sum
    - cloud_assets_collection_num_failed_tasks_total
    - cloud_assets_collection_num_successful_tasks_total
    - cloud_assets_collection_num_content_archive_files_download_failures_total
    - cloud_assets_collection_num_rit_files_yaml_parsing_failure_total
    - cloud_assets_collection_num_rits_content_version_failures_total
    - cloud_assets_collection_num_wrong_format_content_files_total
    - cloud_assets_collection_message_processing_duration_seconds_bucket
    - cloud_assets_collection_message_processing_duration_seconds_count
    - cloud_assets_collection_message_processing_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum
    - cloud_assets_collection_platform_api_request_duration_seconds_bucket
    - cloud_assets_collection_platform_api_request_duration_seconds_count
    - cloud_assets_collection_platform_api_request_duration_seconds_sum
    - cloud_assets_collection_rit_files_duplications
    - cc_cache_update_time_seconds_bucket
    - classification_mgmt_bigquery_write_error_total
    - classification_mgmt_pubsub_publish_error_total
    - classification_mgmt_content_delivery_error_total
    - classification_mgmt_dashboard_error_total
    - classification_mgmt_dashboard_business_total
    - cns_asset_conversion_errors_total
    - cns_engine_errors_total
    - cns_invalid_policies
    - cns_invalid_rules
    - cns_issues_emitter_duration_seconds_count
    - cns_issues_emitter_errors_total
    - cns_issues_emitter_queue_size
    - cns_job_duration_seconds
    - cns_job_init_failed
    - cns_num_assets
    - cns_num_policies
    - cns_num_rules
    - cns_policy_issues_generated
    - cns_rule_duration_seconds
    - cns_rule_findings_generated
    - cold_storage_aggregator_.+.
    - cold_storage_datasets_aggregator_aggregator_compression_rate
    - cold_storage_datasets_aggregator_aggregator_process_duration
    - cold_storage_datasets_aggregator_committed_object_size
    - cold_storage_datasets_aggregator_compression_job_status
    - cold_storage_datasets_aggregator_dataset_errors_count
    - cold_storage_datasets_aggregator_delete_objects_count
    - cold_storage_datasets_aggregator_processed_bytes_total
    - cold_storage_datasets_aggregator_processed_objects_count
    - cold_storage_datasets_aggregator_raw_object_size
    - cold_storage_datasets_aggregator_spawned_aggregators_count
    - cold_tables_sync_job_failure_total
    - contextual_search_graph_neo4j_query_execution_time_millis_bucket
    - contextual_search_graph_neo4j_query_execution_time_millis_count
    - contextual_search_graph_neo4j_query_execution_time_millis_created
    - contextual_search_graph_neo4j_query_execution_time_millis_sum
    - cortex_gw_messages_processor_.+
    - cortex_cdl_to_clcs_migration_failed_total
    - cortex_cdl_to_clcs_migration_succeeded_total
    - cortex_platform_http_request_duration_highr_seconds_count
    - cortex_platform_http_request_duration_highr_seconds_sum
    - cortex_platform_http_request_duration_seconds_count
    - cortex_platform_http_request_duration_seconds_sum
    - cortex_platform_http_request_size_bytes_count
    - cortex_platform_http_request_size_bytes_sum
    - cortex_platform_http_requests_total
    - cortex_platform_http_response_size_bytes_count
    - cortex_platform_http_response_size_bytes_sum
    - cronus_active_connections_total
    - cronus_client_client_roundtrip_latency_sec_bucket
    - cronus_client_connection_wait_duration_seconds_bucket
    - cronus_client_rate_limited_requests_total
    - cronus_client_requests_total
    - cronus_dao_inserts_total
    - cronus_db_repair_dropped_entries_total
    - cronus_handler_wait_time_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_count
    - cronus_hotkeys_count_delay_duration_seconds_sum
    - cronus_hotkeys_count_duration_seconds_bucket
    - cronus_hotkeys_count_duration_seconds_count
    - cronus_hotkeys_count_duration_seconds_sum
    - cronus_hotkeys_count_in_time_window
    - cronus_hotkeys_events_count_in_time_window
    - cronus_hotkeys_threshold_in_time_window
    - cronus_last_processed_index
    - cronus_log_cache_get_ops
    - cronus_operator_cluster_hotkeys_count_in_time_window
    - cronus_operator_cluster_hotkeys_events_count_in_time_window
    - cronus_rebalance_download_bytes_total
    - cronus_rebalance_read_bytes_total
    - cronus_rebalance_upload_bytes_total
    - cronus_request_duration_seconds_bucket
    - cronus_request_process_duration_seconds_bucket
    - cronus_requests_queue_stream_feed_wait_duration_seconds_bucket
    - cronus_requests_total
    - cronus_rows_index_latency_seconds_bucket
    - cronus_storage_latency_seconds_bucket
    - cronus_throttled_write_requests_total
    - cronus_tree_index_compaction_duration_seconds_bucket
    - cs_migration_result_ratio
    - cs_migration_errors_total
    - cwp_.* # whitelist all cwp_.* metrics until PC2 Feb Release (CRTX-132683)
    - cwp_ads_scanned_assets_without_volumes
    - cwp_api_latency_seconds_bucket
    - cwp_api_latency_seconds_count
    - cwp_api_latency_seconds_sum
    - cwp_api_requests_total
    - cwp_ci_analyzer_api_requests_total
    - cwp_dangling_resources_total
    - cwp_instance_type_errors_total
    - cwp_malwaredetection_wildfire_latency_seconds_bucket
    - cwp_malwaredetection_wildfire_latency_seconds_count
    - cwp_malwaredetection_wildfire_latency_seconds_sum
    - cwp_sp_snapshot_auto_deleted_total
    - cwp_sp_snapshot_csp_requests_total
    - cwp_sp_snapshot_dangling_total
    - cwp_sp_snapshot_lifetime_seconds_bucket
    - cwp_sp_snapshot_lifetime_seconds_count
    - cwp_sp_snapshot_lifetime_seconds_sum
    - cwp_sp_snapshot_operation_duration_seconds_bucket
    - cwp_sp_snapshot_operation_duration_seconds_count
    - cwp_sp_snapshot_operation_duration_seconds_sum
    - cwp_sp_snapshot_request_avg_waiting_time_seconds
    - cwp_sp_snapshot_requests_missed_sla
    - dashboard_engine_.+
    - data_ingestion_health_ingestion_alerts_total
    - dbre_backup_succeeded
    - neo4j_dbms_page_cache_hit_ratio
    - neo4j_dbms_vm_heap_used
    - neo4j_dbms_vm_gc_time_g1_old_generation_total
    - neo4j_dbms_vm_gc_time_g1_young_generation_total
    - dml_.+
    - dms_.+
    - dp_asset_associations_pipeline_association_big_query_errors
    - dp_asset_associations_pipeline_association_public_ip_filter
    - dp_asset_associations_pipeline_association_stats
    - dp_asset_associations_pipeline_assets_errors
    - dp_asset_associations_pipeline_assets_total
    - dp_asset_associations_wlm_low_fidelity_asset_cleanup_total
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_associations_pipeline_assets_batch_size_bucket
    - dp_asset_associations_pipeline_assets_batch_size_count
    - dp_asset_associations_pipeline_assets_batch_size_sum
    - dp_asset_associations_pipeline_association_big_query_result_size_bucket
    - dp_asset_associations_pipeline_association_big_query_result_size_count
    - dp_asset_associations_pipeline_association_big_query_result_size_sum
    - dp_asset_associations_pipeline_metablob_errors
    - dp_asset_associations_pipeline_ratelimit_errors
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_pipeline_metablob_errors
    - dp_asset_pipeline_performance_bucket
    - dp_asset_pipeline_performance_count
    - dp_asset_pipeline_performance_sum
    - dp_asset_pipeline_rows_in_metablobs_bucket
    - dp_asset_pipeline_rows_in_metablobs_count
    - dp_asset_pipeline_rows_in_metablobs_sum
    - dp_asset_pipeline_skip_get_deleted_assets
    - dp_asset_pipeline_operation_total
    - dspm_dt_adc_data_writer_success_total
    - dspm_dt_adc_data_writer_total_written_total
    - dspm_dt_bigquery_job_affected_rows_total
    - dspm_dt_bigquery_job_duration_seconds_count
    - dspm_dt_bigquery_job_duration_seconds_max
    - dspm_dt_bigquery_job_duration_seconds_sum
    - dspm_dt_bigquery_job_processed_bytes_total
    - dspm_dt_bigquery_job_processed_partitions_total
    - dspm_dt_bigquery_job_slot_millis_total
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_post_processing_listener_message_successes_total
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_file_analysis_listener_message_successes_total
    - dspm_dt_fda_files_processed_total
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_column_analysis_listener_message_successes_total
    - dspm_dt_fda_columns_processed_total
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_analysis_listener_message_successes_total
    - dspm_dt_fda_assets_processed_total
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_inventory_listener_message_failures_total
    - dspm_dt_fda_asset_inventory_listener_message_successes_total
    - dspm_dt_fda_data_writer_success_total
    - dspm_dt_fda_data_writer_total_written_total
    - dspm_dt_fda_replication_oldest_time_seconds
    - dspm_dt_mac_assets_publish_total
    - dspm_dt_mac_discovery_delete_total
    - dspm_dt_mac_discovery_publish_total
    - dspm_dt_mac_query_execution_duration_seconds_count
    - dspm_dt_mac_query_execution_duration_seconds_max
    - dspm_dt_mac_query_execution_duration_seconds_sum
    - dspm_dt_mac_query_execution_failure_count_total
    - dspm_dt_mac_query_execution_success_count_total
    - dss_enabled
    - dss_last_updated_on_last_change
    - dss_sync_times_difference
    - edr_.+
    - effective_ip_range_monitoring_overlapping_rules
    - egress_aggregator_aggregator_compression_rate
    - egress_aggregator_committed_object_size
    - egress_aggregator_compression_job_status
    - egress_aggregator_delete_objects_count
    - egress_aggregator_processed_bytes_total
    - egress_aggregator_processed_objects_count
    - egress_aggregator_raw_object_size
    - egress_aggregator_spawned_aggregators_count
    - email_attachment_missing_file
    - email_attachment_pending_decrypt
    - email_attachment_pending_wf_submit
    - email_attachment_raised_alert
    - email_attachment_submitted_to_wf
    - email_attachment_unsupported_file_type
    - email_relay_attachment_submitted_to_wf_total
    - email_relay_attachment_verdict_total
    - email_relay_attachments_total
    - email_relay_gcs_latency_milliseconds_count
    - email_relay_gcs_latency_milliseconds_sum
    - email_relay_steps_latency_milliseconds_count
    - email_relay_steps_latency_milliseconds_sum
    - email_relay_wildfire_error_codes_total
    - email_relay_wildfire_latency_milliseconds_count
    - email_relay_wildfire_latency_milliseconds_sum
    - email_relay_wildfire_unsupported_files_total
    - email_relay_emails_total
    - email_relay_alerts_total
    - email_relay_pubsub_total
    - email_relay_deleted_attachments_total
    - email_relay_attachments_over_limit_total
    - email_relay_scheduler_task_executions_total
    - em_integration_asset_processor_errors_total
    - ext_controller_element_duration_seconds_count
    - ext_controller_element_duration_seconds_sum
    - ext_controller_element_status
    - failed_requests_total
    - finding_pubsub_message_histogram_bucket
    - finding_pubsub_message_histogram_count
    - finding_pubsub_message_histogram_sum
    - findings_table_fetching_time_seconds_bucket
    - findings_table_fetching_time_seconds_created
    - findings_table_fetching_time_seconds_sum
    - finding_operation_total
    - finding_metablob_performance_bucket
    - finding_metablob_performance_count
    - finding_metablob_performance_sum
    - xdr_forensics_sams
    - xdr_forensics_hunt_lag_hours
    - gcs_notification_failed_subscriptions
    - GnzEdrPipeline_.+
    - GnzGlobal_DynamicConfig_get_config_failures_total
    - GnzGlobal_pithos_active_streams
    - GnzGlobal_pithos_aggregated_bytes_total
    - GnzGlobal_pithos_aggregated_objects_total
    - GnzGlobal_pithos_aggregation_duration_seconds
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_count
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_sum
    - GnzGlobal_pithos_committed_objects_total
    - GnzGlobal_pithos_dataset_aggregators
    - GnzGlobal_pithos_streamed_bytes_total
    - GnzMbIngester_.+
    - GnzStoryBuilder_.+
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - gonzo_server_.+
    - hpl_json_.+
    - http_requests_total
    - http_server_requests_seconds_count
    - http_server_duration_milliseconds_bucket
    - http_server_duration_milliseconds_count
    - http_server_duration_milliseconds_sum
    - http_server_request_duration_seconds_bucket
    - http_server_request_duration_seconds_count
    - http_server_request_duration_seconds_sum
    - http_server_response_size_bytes_sum
    - http_server_response_size_bytes_count
    - ingestion_quota_exceeded
    - inline_scanner_asset_change_event_failed_total
    - inline_scanner_asset_change_event_ignored_total
    - inline_scanner_asset_change_event_received_total
    - inline_scanner_asset_change_event_replayed_total
    - inline_scanner_assets_finding
    - inline_scanner_assets_scanned_total
    - inline_scanner_cloud_account_fetched
    - inline_scanner_findings_published_failed_total
    - inline_scanner_findings_published_replayed_total
    - inline_scanner_findings_published_success_total
    - inline_scanner_scanlog_generated
    - inline_scanner_scanlog_exported
    - inline_scanner_scanlog_export_timer_milliseconds_sum
    - inline_scanner_asset_change_scan_timer_milliseconds_sum
    - inline_scanner_scanlog_exported_size_kb_sum
    - itdr_data_pipeline_asset_metadata_error_count
    - itdr_data_pipeline_asset_metadata_id_not_found_count
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_sum
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_sum
    - itdr_data_pipeline_asset_updates_count_total
    - itdr_data_pipeline_asset_updates_duration_seconds_bucket
    - itdr_data_pipeline_asset_updates_duration_seconds_count
    - itdr_data_pipeline_asset_updates_duration_seconds_sum
    - itdr_data_pipeline_asset_updates_error_count_total
    - itdr_data_pipeline_asset_updates_manager_input_count_total
    - itdr_data_pipeline_asset_updates_manager_input_error_count_total
    - itdr_data_pipeline_cdc_error_count
    - itdr_data_pipeline_cdc_write_count
    - itdr_data_pipeline_cdc_write_duration_seconds_bucket
    - itdr_data_pipeline_cdc_write_duration_seconds_count
    - itdr_data_pipeline_cdc_write_duration_seconds_sum
    - itdr_data_pipeline_cie_read_row_error
    - itdr_data_pipeline_cie_row_errors_processed
    - itdr_data_pipeline_cie_rows_processed
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_deleted_assets_create_extended_fields_error
    - itdr_data_pipeline_deleted_assets_read_row_error
    - itdr_data_pipeline_deleted_assets_row_errors_processed
    - itdr_data_pipeline_deleted_assets_rows_processed
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_dss_sync_input_count
    - itdr_data_pipeline_dss_sync_input_error_count
    - itdr_data_pipeline_pubsub_output_count
    - itdr_data_pipeline_pubsub_output_error_count
    - itdr_data_pipeline_risk_handler_error_count
    - itdr_data_pipeline_risk_handler_read_duration_seconds_bucket
    - itdr_data_pipeline_risk_handler_read_duration_seconds_count
    - itdr_data_pipeline_risk_handler_read_duration_seconds_sum
    - itdr_data_pipeline_uai_asset_ingestion_error_count
    - itdr_data_pipeline_uai_asset_patch_count
    - itdr_data_pipeline_uai_asset_patch_error_count
    - itdr_risk_processor_bigquery_output_counter
    - itdr_risk_processor_bigquery_output_error_counter
    - itdr_risk_processor_case_update_without_assets_counter
    - itdr_risk_processor_dao_access_duration_seconds_bucket
    - itdr_risk_processor_dao_access_duration_seconds_count
    - itdr_risk_processor_dao_access_duration_seconds_sum
    - itdr_risk_processor_db_migration_error_counter
    - itdr_risk_processor_pubsub_input_counter
    - itdr_risk_processor_pubsub_input_error_counter
    - itdr_risk_processor_pubsub_output_counter
    - itdr_risk_processor_pubsub_output_error_counter
    - itdr_risk_processor_risk_cron_duration_seconds_bucket
    - itdr_risk_processor_risk_cron_duration_seconds_count
    - itdr_risk_processor_risk_cron_duration_seconds_sum
    - itdr_data_pipeline_assets_retention_duration_seconds_count
    - itdr_data_pipeline_assets_retention_duration_seconds_bucket
    - itdr_data_pipeline_assets_retention_duration_seconds_sum
    - itdr_data_pipeline_retention_assets_read_row_error
    - itdr_data_pipeline_retention_assets_rows_processed
    - itdr_data_pipeline_baseline_read_row_error_total
    - itdr_data_pipeline_baseline_rows_processed_total
    - itdr_data_pipeline_baseline_row_errors_processed_total
    - itdr_data_pipeline_baseline_query_iterator_errors_total
    - itdr_data_pipeline_baseline_outputs_errors_total
    - itdr_data_pipeline_baseline_risk_insights_enrich_error_total
    - itdr_data_pipeline_baseline_risk_insights_enriched_assets_total
    - itdr_data_pipeline_baseline_okta_users_found_total
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_baseline_handler_handled_rows_total
    - itdr_data_pipeline_baseline_metadata_error_counter_total
    - itdr_data_pipeline_baseline_metadata_inserted_counter_total
    - itdr_data_pipeline_cap_data_repository_duration_seconds_count
    - itdr_data_pipeline_cap_data_repository_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_repository_duration_seconds_sum
    - itdr_data_pipeline_cap_data_repository_errors_total
    - itdr_data_pipeline_cap_data_publish_success_total
    - itdr_data_pipeline_cap_data_publish_errors_total
    - itdr_data_pipeline_cap_data_upload_duration_seconds_count
    - itdr_data_pipeline_cap_data_upload_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_upload_duration_seconds_sum
    - itdr_data_pipeline_cap_data_upload_success_total
    - itdr_data_pipeline_cap_data_upload_errors_total
    - itdr_data_pipeline_zip_manager_create_new_zip_file_errors_total
    - itdr_data_pipeline_zip_manager_create_file_in_zip_errors_total
    - itdr_data_pipeline_zip_manager_write_data_to_file_in_zip_errors_total
    - itdr_data_pipeline_zip_manager_close_zip_writer_errors_total
    - itdr_data_pipeline_zip_manager_close_new_zip_file_errors_total
    - itdr_data_pipeline_realtime_updater_duration_seconds_count
    - itdr_data_pipeline_realtime_updater_duration_seconds_bucket
    - itdr_data_pipeline_realtime_updater_duration_seconds_sum
    - itdr_data_pipeline_realtime_updater_rows_processed_total
    - itdr_data_pipeline_realtime_updater_process_errors_total
    - itdr_data_pipeline_realtime_log_insert_rows_handler_error_total
    - itdr_data_pipeline_realtime_log_insert_rows_handler_inserted_rows_total
    - itdr_data_pipeline_password_analyzer_input_count
    - itdr_data_pipeline_password_analyzer_input_error_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_bucket
    - itdr_data_pipeline_pwd_analyzer_process_duration_sum
    - itdr_data_pipeline_pwd_analyzer_users_proceed
    - itdr_data_pipeline_pwd_analyzer_read_object_error
    - itdr_data_pipeline_pwd_analyzer_delete_object_error
    - itdr_data_pipeline_pwd_analyzer_password_handler_error
    - itdr_data_pipeline_pwd_analyzer_mandatory_file_missing
    - itdr_data_pipeline_pwd_analyzer_user_enricher_error
    - itdr_data_pipeline_pwd_analyzer_password_analyzer_error
    - itdr_data_pipeline_pwd_analyzer_handle_open_findings_error
    - itdr_data_pipeline_pwd_analyzer_handle_close_findings_error
    - itdr_data_pipeline_pwd_analyzer_dao_read_error
    - itdr_data_pipeline_pwd_analyzer_matches
    - itdr_data_pipeline_pwd_analyzer_access_salt_error
    - itdr_data_pipeline_pwd_analyzer_extract_data_from_zip_error
    - itdr_data_pipeline_salt_rotation_count
    - itdr_data_pipeline_salt_rotation_error
    - itdr_data_pipeline_salt_add_version_count
    - itdr_data_pipeline_salt_add_version_error
    - itdr_data_pipeline_salt_get_active_version_error
    - itdr_data_pipeline_salt_remove_version_error
    - itdr_data_pipeline_redis_error_count_total
    - itdr_data_pipeline_agent_pwd_sync
    - itdr_data_pipeline_cie_enricher_duration_bucket
    - itdr_data_pipeline_cie_enricher_duration_count
    - itdr_data_pipeline_cie_enricher_duration_sum
    - itdr_data_pipeline_cie_enricher_error_count
    - itdr_data_pipeline_risk_handler_table_doesnt_exists_count
    - itdr_data_pipeline_dp_bus_error_count_total
    - itdr_data_pipeline_ad_hygiene_input_count_total
    - itdr_data_pipeline_ad_hygiene_input_error_count_total
    - itdr_data_pipeline_ad_hygiene_dtos_processed_total
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_bucket
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_count
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_sum
    - known_attachment_malwares
    - master_tenant_listener_is_stale
    - memsql_counter_bytes_received
    - memsql_counter_bytes_sent
    - memsql_counter_connections
    - memsql_counter_failed_read_queries
    - memsql_counter_failed_write_queries
    - memsql_counter_successful_read_queries
    - memsql_counter_successful_write_queries
    - memsql_distributed_partitions_offline
    - memsql_distributed_partitions_online
    - memsql_distributed_partitions_total
    - memsql_status_aborted_connects
    - memsql_status_failed_read_queries
    - memsql_status_failed_write_queries
    - memsql_workload_management_total_queries_cancelled_since_startup
    - memsql_workload_management_total_queries_finished_since_startup
    - memsql_workload_management_total_queries_started_since_startup
    - metrics_aggregator_.+
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed_seconds
    - number_of_cloud_accounts
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed
    - neo4j_.*bolt_connections.*
    - neo4j_.*bolt_messages_received.*
    - neo4j_.*bolt_messages_started.*
    - neo4j_dbms_pool_bolt_total_size
    - neo4j_dbms_pool_bolt_total_used
    - neo4j_dbms_pool_bolt_used_heap
    - neo4j_.*check_point.*
    - neo4j_.*cypher_replan_events.*
    - neo4j_.*cypher_cache.*
    - neo4j_.*pool_transaction.*_total_used
    - neo4j_.*pool_transaction.*_used_heap
    - neo4j_.*store_size.*
    - neo4j_.*transaction_active_read
    - neo4j_.*transaction_active_write
    - neo4j_.*transaction_committed.*
    - neo4j_.*transaction_peak_concurrent.*
    - neo4j_.*transaction_rollbacks.*
    - neo4j_.*page_cache_hit.*
    - neo4j_.*page_cache_page_faults.*
    - neo4j_.*page_cache_usage_ratio
    - neo4j_.*vm_file_descriptors_count
    - neo4j_.*vm_gc_time.*
    - neo4j_.*vm_heap_used
    - netscan_authentication_success
    - netscan_avg_target_size
    - netscan_host_properties
    - netscan_hosts_currently_scanning
    - netscan_kms_error_total
    - netscan_processor_cns_host_dead_in_scan
    - netscan_processor_cns_host_max_tuple_batch_size
    - netscan_processor_cns_host_prior_scan_age_bucket
    - netscan_processor_cns_host_unresponsive
    - netscan_processor_dao_access_duration_seconds_bucket
    - netscan_processor_db_migration_error
    - netscan_processor_netscan_results_batch_process_bucket
    - netscan_processor_scan_result_batch_failure
    - netscan_processor_scan_result_batch_size_bucket
    - netscan_processor_scan_result_incompatible
    - netscan_processor_scan_result_match
    - netscan_processor_scan_result_message_count_bucket
    - netscan_processor_scan_result_new_cns_host
    - netscan_processor_scan_task_processed
    - netscan_processor_scan_result_unrelated_hostname
    - netscan_processor_scan_result_with
    - netscan_processor_uai_pubsub_output_error
    - netscan_scan_duration_count
    - netscan_scan_duration_created
    - netscan_scan_duration_sum
    - netscan_scan_timeouts
    - netscan_scans_configured
    - netscan_scans_in_flight
    - netscan_scans_scheduled
    - netscan_scans_status
    - netscan_test_result 
    - notification_migration_errors_total
    - notification_migration_result_ratio
    - otelcol_.+
    - partyzaurus_.+
    - platform_compliance_used_assets_count
    - platform_compliance_active_assessment_profile_count
    - platform_compliance_calculation_run_time
    - platform_compliance_calculation_last_run
    - platform_compliance_controls_table_size
    - platform_compliance_standards_table_size
    - platform_compliance_tenant
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - prom_gonzo_storage_adapter_.+
    - pz_.+
    - pz_schema_manager_.+
    - policy_migration_result_ratio
    - policy_migration_errors_total
    - rule_migration_result
    - rule_migration_errors_total
    - xsoar_podman_cpu
    - xsoar_podman_memory
    - xsoar_podman_uptime
    - xsoar_rc.+
    - spur_dms_actual_batch_size_bucket
    - spur_dms_actual_batch_size_count
    - spur_dms_actual_batch_size_sum
    - spur_dms_cache_size
    - spur_dms_fetch_duration_seconds_bucket
    - spur_dms_fetch_duration_seconds_count
    - spur_dms_fetch_duration_seconds_sum
    - spur_dms_fetch_errors_count
    - spur_dms_inactive_until_unix_milli
    - spur_dms_ip_parse_error
    - spur_dms_poll_failure_count
    - spur_dms_queried_count
    - spur_dms_request_count
    - spur_dms_skipped_ips_count
    - spur_dms_spur_generation_time_unix_milli
    - spur_pz_actual_batch_size_bucket
    - spur_pz_actual_batch_size_count
    - spur_pz_actual_batch_size_sum
    - spur_pz_cache_size
    - spur_pz_fetch_duration_seconds_bucket
    - spur_pz_fetch_duration_seconds_count
    - spur_pz_fetch_duration_seconds_sum
    - spur_pz_fetch_errors_count
    - spur_pz_inactive_until_unix_milli
    - spur_pz_ip_parse_error
    - spur_pz_poll_failure_count
    - spur_pz_queried_count
    - spur_pz_request_count
    - spur_pz_skipped_ips_count
    - spur_pz_spur_generation_time_unix_milli
    - storybuilder_.+
    - support_case_auto_generate_tsf_request_timeout_total
    - support_case_failed_auto_generate_tsf_request_total
    - support_case_failed_upload_files_total
    - support_case_number_of_support_case_successfully_created_total
    - support_case_number_of_support_case_failed_to_create_total
    - support_case_success_auto_generate_tsf_request_total
    - support_case_success_upload_files_total
    - storybuilder_filtered_events_total
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used
    - uai_aggregated_assets_cdc_delay_seconds
    - uai_api_errors_count_total
    - uai_api_requests_count_total
    - uai_endpoints_health_check_failed
    - vectorized_matcher_current_content_version_exporting_time_in_seconds
    - vectorized_matcher_current_content_version_loading_time_in_seconds
    - verdict_manager_batches_completed
    - verdict_manager_verdicts_fetched
    - verdict_manager_findings_created
    - verdict_manager_findings_closed
    - verdict_manager_findings_publish_failed
    - verdict_manager_findings_publish_success
    - verdict_manager_issues_created
    - verdict_manager_issues_updated
    - verdict_manager_issues_closed
    - verdict_manager_issues_batch_publish_success
    - verdict_manager_issues_batch_publish_failed
    - verdict_manager_reconcile_failed
    - vsg_reconcile_time_bucket
    - vsg_reconcile_time_count
    - vsg_reconcile_time_sum
    - vsg_oom_scaler_applied_memory_resources
    - vsg_oom_scaler_last_applied_timestamp
    - vsg_oom_scaler_last_reconcile_timestamp
    - vsg_oom_scaler_pod_request_memory_max
    - vsg_oom_scaler_reconcile_time_bucket
    - vsg_oom_scaler_reconcile_time_count
    - vsg_oom_scaler_reconcile_time_sum
    - vsg_oom_scaler_resolve_source_metric_bucket
    - vsg_oom_scaler_resolve_source_metric_count
    - vsg_oom_scaler_resolve_source_metric_sum
    - vsg_oom_scaler_target_memory_resources
    - vsg_oom_scaler_under_cooldown_timestamp
    - vsg_vertical_scaler_applied_cpu_resources
    - vsg_vertical_scaler_applied_memory_resources
    - vsg_vertical_scaler_current_step_thresholds
    - vsg_vertical_scaler_hpa_invalid_timestamp
    - vsg_vertical_scaler_last_applied_timestamp
    - vsg_vertical_scaler_last_reconcile_timestamp
    - vsg_vertical_scaler_reconcile_time_bucket
    - vsg_vertical_scaler_reconcile_time_count
    - vsg_vertical_scaler_reconcile_time_sum
    - vsg_vertical_scaler_resolve_source_metric_bucket
    - vsg_vertical_scaler_resolve_source_metric_count
    - vsg_vertical_scaler_resolve_source_metric_sum
    - vsg_vertical_scaler_scaled_replicas
    - vsg_vertical_scaler_source_metric_result
    - vsg_vertical_scaler_target_cpu_resources
    - vsg_vertical_scaler_target_memory_resources
    - vsg_vertical_scaler_target_replicas
    - vsg_vertical_scaler_under_cooldown_timestamp
    - vsg_zero_scaler_last_applied_timestamp
    - vsg_zero_scaler_last_reconcile_timestamp
    - vsg_zero_scaler_manually_downscaled_timestamp
    - vsg_zero_scaler_reconcile_time_bucket
    - vsg_zero_scaler_reconcile_time_count
    - vsg_zero_scaler_reconcile_time_sum
    - vsg_zero_scaler_resolve_source_metric_bucket
    - vsg_zero_scaler_resolve_source_metric_count
    - vsg_zero_scaler_resolve_source_metric_sum
    - vsg_zero_scaler_scaled_replicas
    - vsg_zero_scaler_source_metric_result
    - vsg_zero_scaler_target_replicas
    - vsg_zero_scaler_thresholds
    - vuln_action_plan_creation_failure_total
    - vuln_action_plan_creation_metrics
    - vuln_action_plan_recon_failure_total
    - vuln_action_plan_recon_metrics
    - vxp_bigquery_rows_read_total
    - vxp_bigquery_slots_used_total
    - vxp_http_server_requests_count
    - vxp_http_server_requests_sum
    - vxp_job_run_time_count
    - vxp_issues_published_total
    - vxp_job_run_time_sum
    - vxp_policy_sync_actions_total
    - vxp_policy_sync_findings_total
    - vxp_pubsub_errors_total
    - vxp_pubsub_processing_latency_bucket
    - vxp_sync_cve_coverage_job_malformed_rows_total
    - wf_vs_get_verdicts_request_latency_seconds_bucket
    - wf_vs_get_verdicts_request_failed_total
    - wf_vs_get_verdicts_size
    - wf_vs_set_upload_request_failed
    - wf_vs_set_upload_request_success
    - wlm_monitoring_.+
    - xcloud_assets_count
    - xcloud_discovery_hours_delay
    - xcloud_inventory_strategies_count_by_provider_and_status
    - xcloud_onboarding_message
    - xcloud_strategies_delay_hours_by_service
    - xdm_.+
    - xdr_active_lightweight_agents_7d
    - xdr_addons_license_status
    - xdr_agent.+
    - xdr_alert_domain_migration_error
    - xdr_alert_source_delay_time
    - xdr_alert_sync_tags_databases_error_total
    - xdr_alerts_.+
    - xdr_alerts_fetcher.+
    - xdr_api_errors_total
    - xdr_api_waitress_allocated_threads
    - xdr_api_waitress_occupied_threads
    - xdr_api_waitress_queued_requests
    - xdr_asm_mega_join_cache_creation_error_total
    - xdr_asm_mega_join_cache_delay
    - xdr_asm_xpanse_replication_delay
    - xdr_asset_command_.+
    - xdr_asset_score_changes_received_created
    - xdr_asset_score_changes_received_total
    - xdr_asset_score_changes_sent_to_cie_created
    - xdr_asset_score_changes_sent_to_cie_total
    - xdr_bigquery_bytes_processed_count
    - xdr_bigquery_bytes_processed_sum
    - xdr_bigquery_execution_time_count
    - xdr_bigquery_execution_time_sum
    - xdr_bigquery_queue_time_count
    - xdr_bigquery_queue_time_sum
    - xdr_bigquery_zslots_count
    - xdr_bigquery_zslots_sum
    - xdr_biocs_assigned_to_profiles_count
    - xdr_bq_stats_not_delivered_from_pubsub_total
    - xdr_bq_stats_redis_list_length_maxed_total
    - xdr_broker_.+
    - xdr_calc_.+
    - xdr_card_.+
    - xdr_case_.+
    - xdr_cie_risk_score_errors_created
    - xdr_cie_risk_score_errors_total
    - xdr_calc_last_association_replication_time
    - xdr_clcs_counter
    - xdr_clcs_multi_csp_connected
    - xdr_clcs_multi_region_connected_count
    - xdr_clcs_multi_region_enabled
    - xdr_clcs_multi_salesforce_enabled
    - xdr_clcs_subscriptions_data
    - xdr_cloud_agents_count
    - xdr_cloud_license_count
    - xdr_cloud_license_purchased
    - xdr_cloud_pro_agents_count
    - xdr_cold_storage_oldest_raw_object
    - xdr_collection_.+
    - xdr_collector_.+
    - xdr_count_operational_status_and_os_type
    - xdr_count_operational_status_description_and_os_type
    - xdr_cts_active_sessions
    - xdr_cts_active_tokens
    - xdr_cts_cache_hits_total
    - xdr_cts_cache_miss_total
    - xdr_cts_fresh_token_total
    - xdr_cts_request_time_count
    - xdr_cts_request_time_sum
    - xdr_cts_request_total
    - xdr_cts_sts_error_total
    - xdr_cts_sts_unauthorized_total
    - xdr_cts_waitress_allocated_threads
    - xdr_cts_waitress_occupied_threads
    - xdr_cts_waitress_queued_requests
    - xdr_current_connected_agents
    - xdr_current_connected_edr_agents
    - xdr_data_usage_24_hours
    - xdr_device_control_license
    - xdr_dss_.*
    - xdr_dumpster_auto_upload_config
    - xdr_dumpster_auto_upload_dumps_count
    - xdr_edr_active_agents_24h
    - xdr_edr_agents_24_hours
    - xdr_edr_agents_count
    - xdr_edr_license_count
    - xdr_edr_license_expiration
    - xdr_edr_license_purchased
    - xdr_egress_oldest_raw_object
    - xdr_email_service.+
    - xdr_email_to_issue_latency_bucket
    - xdr_email_to_issue_latency_count
    - xdr_email_to_issue_latency_sum
    - xdr_epp_agents_count
    - xdr_epp_license_count
    - xdr_epp_license_expiration
    - xdr_epp_license_purchased
    - xdr_forensics_agents
    - xdr_forensics_licenses
    - xdr_forensics_tenant
    - xdr_get_unused_api_key_ids
    - xdr_gvs_.+
    - xdr_host_insights_is_enabled
    - xdr_hpa_xql_pubsub_metric
    - xdr_hpa_pipeline_pubsub_metric
    - xdr_hpa_dms_pubsub_metric
    - xdr_incident_alert_data_sync_error_total
    - xdr_incidents_by_status_avg
    - xdr_incidents_by_status_count
    - xdr_informative_btp_alerts
    - xdr_init_app_status
    - xdr_init_app_took
    - xdr_invalidate_user_role_failure
    - xdr_investigation_in_progress
    - xdr_ios_large_digest_report_total
    - xdr_issue_fetcher_handle_message_time_count
    - xdr_issue_fetcher_handle_message_time_created
    - xdr_issue_fetcher_handle_message_time_sum
    - xdr_issue_fetcher_parse_time_count
    - xdr_issue_fetcher_parse_time_created
    - xdr_issue_fetcher_parse_time_sum
    - xdr_issue_handle_message_time_count
    - xdr_issue_handle_message_time_created
    - xdr_issue_handle_message_time_sum
    - xdr_issue_send_to_dlq_total
    - xdr_issue_rate_limit_total
    - xdr_issue_updater_handle_message_time_count
    - xdr_issue_updater_handle_message_time_sum
    - xdr_issue_updater_handle_message_time_created
    - xdr_issue_updater_optimistic_lock_total
    - xdr_kpi_.+
    - xdr_license_fetch_failures
    - xdr_logging_.*
    - xdr_mail_event_processor_.+
    - xdr_mailing_queue_count
    - xdr_managed_tenant_monitor_info
    - xdr_matching_service_detection_queue_depth
    - xdr_mdr_license
    - xdr_forensics_migration_status
    - xdr_mssp_license
    - xdr_mth_license
    - xdr_mysql_.*
    - xdr_nfr_license
    - xdr_notifcation_mail_queue_count
    - xdr_notification_dead_letter_queue_table_count
    - xdr_p2p_discovery_table_count
    - xdr_p2p_scanable_agents_count
    - xdr_phase1_installers_flag
    - xdr_platform_migration_completed
    - xdr_platform_migration_failed
    - xdr_platform_migration_start
    - xdr_platform_migration_status
    - xdr_platform_migration_time_risk
    - xdr_preprocessed_data_batcher_oldest_object
    - xdr_prisma_pairing_status
    - xdr_pz_schema_waitress_occupied_threads
    - xdr_pz_schema_is_dataset_max_keys_reached
    - xdr_pz_schema_bq_external_raw_table_column_count
    - xdr_queries_by_status_count
    - xdr_redis_.+
    - xdr_reports_generator_.+
    - xdr_request_processing_seconds_count
    - xdr_request_processing_seconds_sum
    - xdr_request_response_size_count
    - xdr_request_response_size_sum
    - xdr_retention_enforcement_status
    - xdr_retention_enforcement_task_run
    - xdr_retention_service_sync_retention_stats_failures_total
    - xdr_retention_simulation_status
    - xdr_retryable_grouping_alerts_.+
    - xdr_sbac_enabled
    - xdr_sbac_enabled.+
    - xdr_sbac_mode
    - xdr_schd_task_delay_histogram_bucket
    - xdr_scheduler_wlm_working
    - xdr_search_index.+
    - xdr_story_builder_cpu_utilization
    - xdr_story_builder_max_delay
    - xdr_scouter_to_group_calculation_count
    - xdr_scouter_to_group_calculation_duration
    - xdr_stuck_triage_records
    - xdr_tags_count
    - xdr_tags_sync_not_running
    - xdr_task_processor_process_message_time_count
    - xdr_task_processor_process_message_time_created
    - xdr_task_processor_process_message_time_sum
    - xdr_tenant_configuration_csp
    - xdr_tenant_configuration_subdomain
    - xdr_tenant_configuration_tenant_type
    - xdr_tenant_distribution_list
    - xdr_tenant_configuration_is_migrated_to_platform
    - xdr_tenant_configuration_is_platform
    - xdr_tim_.+
    - xdr_total_agents_by_content_status
    - xdr_total_size_of_stored_edr
    - xdr_unknown_email_issue_type_total
    - xdr_unused_profile_count
    - xdr_upload_management_audit_to_gcs
    - xdr_users_access_to_tenant_count
    - xdr_users_login_to_tenant
    - xdr_va_.+
    - xdr_vulnerability_assessment_.+
    - xdr_wec_.+
    - xdr_wildfire_submit_url_low_priority_req_counter_total
    - xdr_num_of_disabled_rules
    - xdr_num_of_partially_disabled_rules
    - xdr_num_of_enabled_rules
    - xdr_platform_migration_tf_completed
    - xdr_platform_migration_tf_start
    - xdr_whitelist_activation_status
    - xdr_whitelist_activation_status
    - xdr_wildfire_submit_url_req_counter_total
    - xdr_wildfire_submit_url_res_status_total
    - xdr_wlm_count_by_status
    - xdr_wlm_count_table_rows
    - xdr_wlm_oldest_task.+
    - xdr_wlm_pending_tasks.+
    - xdr_wlm_task_delay.+
    - xdr_wlm_task_received_total
    - xdr_wlm_task_started_total
    - xdr_xdr_license_count
    - xdr_xdr_license_expiration
    - xdr_xpanse_alerts_resolver_queue
    - xdr_xpanse_incident_context_injection_failure_total
    - xdr_xsiam_gb_license_count
    - xdr_xsiam_users_license_count
    - xpanse_alert_fetcher.+
    - xpanse_asset_tag_rules_error_total
    - xpanse_compliance_frameworks.+
    - xpanse_data_migration.+
    - xpanse_explorer_ratings_cache_error_total
    - xpanse_global_lookup_request_time_count
    - xpanse_global_lookup_request_time_sum
    - xpanse_global_lookup_request_errors_total
    - xpanse_incident_context_injection_succeed_total
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts
    - xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts
    - XPANSE_MANUAL_SCAN_REQUEST_FAILED_COUNTER_total
    - XPANSE_MANUAL_SCAN_REQUESTED_COUNT_total
    - XPANSE_WEBSITES_DO_NOT_EXIST_ERROR_total
    - XPANSE_WEBSITE_FAILED_LOADING_ITEM_ERROR_total
    - XPANSE_WEBSITE_DETAILS_UNEXPECTED_RESULT_total
    - xpanse_policies.+
    - xpanse_policy.+
    - xpanse_rcs_result_processor_results_processed_count_total
    - xpanse_score_recalculation.+
    - xpanse_scoring_context.+
    - xpanse_tags_to_mysql_sync_error_total
    - xpanse_technology_metadata.+
    - xpanse_threat_events.+
    - xpanse_websites.+
    - xpanse_widget.+
    - xql_.+
    - xql_inflight_api_errors_total
    - xql_inflight_api_response_seconds_count
    - xql_inflight_api_response_seconds_sum
    - xsoar_archive_error
    - xsoar_silent_playbooks_counter
    - xsoar_auto_extract_enrich_all_indicators_bucket
    - xsoar_auto_extract_enrich_indicator_command_bucket
    - xsoar_auto_extract_entry_processing_bucket
    - xsoar_auto_extract_find_indicator_bucket
    - xsoar_auto_extract_format_indicator_bucket
    - xsoar_auto_extract_indicator_enriched_command
    - xsoar_auto_extract_indicator_enrichment_timeout
    - xsoar_auto_extract_indicator_extracted
    - xsoar_auto_extract_indicator_formatted
    - xsoar_auto_extract_indicator_mapped
    - xsoar_auto_extract_map_indicator_bucket
    - xsoar_automation_executions_avg_duration_seconds
    - xsoar_automation_executions_count
    - xsoar_command_executions_avg_duration_seconds
    - xsoar_command_executions_count
    - xsoar_content_backup_snapshot_status
    - xsoar_engine_disconnections
    - xsoar_engine_health_status
    - xsoar_engine_last_health_timestamp
    - xsoar_fetch_execution_fetch_duration_seconds
    - xsoar_fetch_execution_health_status
    - xsoar_fetch_execution_ingestion_duration_seconds
    - xsoar_fetch_execution_pulled_incidents_count
    - xsoar_fetch_execution_pulled_indicators_count
    - xsoar_ha_lock_hijacks_total
    - xsoar_ha_mirroring_error
    - xsoar_ha_secondary_pb_executions_total
    - xsoar_migration_status
    - xsoar_msg_bus_publisher
    - xsoar_msg_bus_messages
    - xsoar_msg_bus_total_active
    - xsoar_msg_bus_oldest
    - xsoar_msg_bus_oldest_nth
    - xsoar_msg_bus_oldest_unhandled_nth
    - xsoar_msg_bus_total
    - xsoar_msg_bus_max_hpa
    - xsoar_msg_bus_min_hpa
    - xsoar_msg_bus_replicas
    - xsoar_msg_bus_reserved_replicas
    - xsoar_msg_bus_subscriber_pull
    - xsoar_msg_bus_subscriber_ack
    - xsoar_msg_bus_subscriber_nack
    - xsoar_msg_bus_dlq_subscriber_pull
    - xsoar_msg_bus_lock_latency_bucket
    - xsoar_msg_bus_insert_latency_bucket
    - xsoar_msg_bus_subscriber_pull_latency_bucket
    - xsoar_msg_bus_subscriber_ack_latency_bucket
    - xsoar_msg_bus_subscriber_nack_latency_bucket
    - xsoar_msg_bus_fetch_metrics_latency_bucket
    - xsoar_msg_bus_oldest_unhandled
    - xsoar_num_archived
    - xsoar_num_data_loss_alert
    - xsoar_num_integrations_updated
    - xsoar_num_of_current_integrations_installed
    - xsoar_num_oversized_alert
    - xsoar_num_panics
    - xsoar_num_sql_errors
    - xsoar_num_un_archived
    - xsoar_num_warroom_errors
    - xsoar_reminder_queue_push_error
    - xsoar_reminder_queue_counter
    - xsoar_reminder_queue_timed_out_entries
    - xsoar_stuck_inv_playbook_detected_total
    - xsoar_un_archive_error
    - xsoar_completed_inv_playbook_total
    - xsoar_inv_playbook_ack_nack_messages_total
    - workflow_failed
    - workflow_success
    - workflow_timeout
    - persistence_requests
    - logback_events_total
    - mongo_polaris_inserts_total
    - controller_runtime_reconcile_panics_total
    - controller_runtime_reconcile_errors_total
    - controller_runtime_terminal_reconcile_errors_total
    - qr_code_image_reader_calls_total
    - qr_code_image_cache_hits_total
    - qr_code_image_cache_size
    - qr_code_image_reader_call_duration_sum
    - qr_code_image_reader_call_duration_count
    - qr_code_image_reader_call_duration_bucket
  debug:
    - analytics_de_v2_vectorized_matcher_detector_count_per_layer_total
    - analytics_de_v2_vectorized_matcher_udf_process_time_count
    - analytics_de_v2_vectorized_matcher_udf_process_time_sum
    - GnzGlobal_.+

prometheus:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - net_conntrack_dialer_conn_failed_total
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - prometheus_build_info
    - prometheus_config_last_reload_success_timestamp_seconds
    - prometheus_config_last_reload_successful
    - prometheus_engine_query_duration_seconds_sum
    - prometheus_http_request_duration_seconds_count
    - prometheus_http_request_duration_seconds_sum
    - prometheus_notifications_alertmanagers_discovered
    - prometheus_remote_storage_.+
    - prometheus_rule_evaluation_duration_seconds_count
    - prometheus_rule_evaluation_duration_seconds_sum
    - prometheus_rule_evaluation_failures_total
    - prometheus_rule_group_duration_seconds_sum
    - prometheus_rule_group_interval_seconds
    - prometheus_rule_group_iterations_missed_total
    - prometheus_rule_group_last_duration_seconds
    - prometheus_rule_group_rules
    - prometheus_sd_consul_rpc_failures_total
    - prometheus_sd_dns_lookup_failures_total
    - prometheus_target_interval_length_seconds
    - prometheus_target_interval_length_seconds_count
    - prometheus_target_scrape_pool_sync_total
    - prometheus_target_scrape_pool_targets
    - prometheus_target_scrapes_exceeded_sample_limit_total
    - prometheus_target_scrapes_sample_duplicate_timestamp_total
    - prometheus_target_scrapes_sample_out_of_bounds_total
    - prometheus_target_scrapes_sample_out_of_order_total
    - prometheus_target_sync_length_seconds_sum
    - prometheus_treecache_zookeeper_failures_total
    - prometheus_tsdb_blocks_loaded
    - prometheus_tsdb_checkpoint_creations_failed_total
    - prometheus_tsdb_checkpoint_deletions_failed_total
    - prometheus_tsdb_compaction_chunk_range_seconds_count
    - prometheus_tsdb_compaction_chunk_range_seconds_sum
    - prometheus_tsdb_compaction_chunk_samples_count
    - prometheus_tsdb_compaction_chunk_samples_sum
    - prometheus_tsdb_compaction_chunk_size_bytes_sum
    - prometheus_tsdb_compaction_duration_seconds_sum
    - prometheus_tsdb_compactions_failed_total
    - prometheus_tsdb_compactions_total
    - prometheus_tsdb_compactions_triggered_total
    - prometheus_tsdb_head_active_appenders
    - prometheus_tsdb_head_chunks
    - prometheus_tsdb_head_chunks_created_total
    - prometheus_tsdb_head_chunks_removed_total
    - prometheus_tsdb_head_gc_duration_seconds_sum
    - prometheus_tsdb_head_max_time
    - prometheus_tsdb_head_min_time
    - prometheus_tsdb_head_samples_appended_total
    - prometheus_tsdb_head_series
    - prometheus_tsdb_head_series_created_total
    - prometheus_tsdb_head_series_removed_total
    - prometheus_tsdb_reloads_failures_total
    - prometheus_tsdb_reloads_total
    - prometheus_tsdb_size_retentions_total
    - prometheus_tsdb_storage_blocks_bytes
    - prometheus_tsdb_symbol_table_size_bytes
    - prometheus_tsdb_time_retentions_total
    - prometheus_tsdb_wal_corruptions_total
    - prometheus_tsdb_wal_fsync_duration_seconds_count
    - prometheus_tsdb_wal_fsync_duration_seconds_sum
    - prometheus_tsdb_wal_truncate_duration_seconds_sum

stackdriver:
  regex:
    - stackdriver_bigquery_dataset_bigquery_googleapis_com_storage_uploaded_bytes
    - stackdriver_k_8_s_container_logging_googleapis_com_user_k_8_s_logs_count_by_application
    - stackdriver_bigquery_dataset_bigquery_googleapis_com_storage_uploaded_row_count
    - stackdriver_exporter_build_info
    - stackdriver_global_bigquery_googleapis_com_query_count
    - stackdriver_global_bigquery_googleapis_com_slots_allocated_for_project_and_job_type
    - stackdriver_k_8_s_container_logging_googleapis_com_byte_count
    - stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age_by_region
    - stackdriver_pubsub_topic_pubsub_googleapis_com_topic_send_message_operation_count
    - stackdriver_uptime_url_monitoring_googleapis_com_uptime_check_check_passed

kubernetes-apiservers:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - kubernetes_build_info
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds

kubernetes-nodes:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - kubelet_node_name
    - kubelet_volume_stats_available_bytes
    - kubelet_volume_stats_capacity_bytes
    - kubelet_volume_stats_used_bytes
    - kubernetes_build_info
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds

es-xsoar-exporter:
  regex:
    - elasticsearch_cluster_health_active_primary_shards
    - elasticsearch_cluster_health_active_shards
    - elasticsearch_cluster_health_delayed_unassigned_shards
    - elasticsearch_cluster_health_initializing_shards
    - elasticsearch_cluster_health_number_of_data_nodes
    - elasticsearch_cluster_health_number_of_nodes
    - elasticsearch_cluster_health_number_of_pending_tasks
    - elasticsearch_cluster_health_relocating_shards
    - elasticsearch_cluster_health_status
    - elasticsearch_cluster_health_total_scrapes
    - elasticsearch_cluster_health_unassigned_shards
    - elasticsearch_filesystem_data_available_bytes
    - elasticsearch_filesystem_data_size_bytes
    - elasticsearch_filesystem_io_stats_device_read_operations_count
    - elasticsearch_filesystem_io_stats_device_read_size_kilobytes_sum
    - elasticsearch_filesystem_io_stats_device_write_operations_count
    - elasticsearch_filesystem_io_stats_device_write_size_kilobytes_sum
    - elasticsearch_indices_deleted_docs_primary
    - elasticsearch_indices_docs
    - elasticsearch_indices_docs_deleted
    - elasticsearch_indices_docs_primary
    - elasticsearch_indices_fielddata_memory_size_bytes
    - elasticsearch_indices_indexing_index_time_seconds_total
    - elasticsearch_indices_indexing_index_total
    - elasticsearch_indices_indexing_is_throttled
    - elasticsearch_indices_merges_docs_total
    - elasticsearch_indices_merges_total_size_bytes_total
    - elasticsearch_indices_merges_total_time_seconds_total
    - elasticsearch_indices_query_cache_memory_size_bytes
    - elasticsearch_indices_refresh_time_seconds_total
    - elasticsearch_indices_request_cache_memory_size_bytes
    - elasticsearch_indices_search_fetch_time_seconds
    - elasticsearch_indices_search_query_time_seconds
    - elasticsearch_indices_search_query_total
    - elasticsearch_indices_search_scroll_time_seconds
    - elasticsearch_indices_search_suggest_time_seconds
    - elasticsearch_indices_segment_count_primary
    - elasticsearch_indices_segment_count_total
    - elasticsearch_indices_segment_memory_bytes_primary
    - elasticsearch_indices_segments_count
    - elasticsearch_indices_segments_doc_values_memory_in_bytes
    - elasticsearch_indices_segments_fixed_bit_set_memory_in_bytes
    - elasticsearch_indices_segments_index_writer_memory_in_bytes
    - elasticsearch_indices_segments_memory_bytes
    - elasticsearch_indices_segments_norms_memory_in_bytes
    - elasticsearch_indices_segments_points_memory_in_bytes
    - elasticsearch_indices_segments_stored_fields_memory_in_bytes
    - elasticsearch_indices_segments_term_vectors_memory_in_bytes
    - elasticsearch_indices_segments_terms_memory_in_bytes
    - elasticsearch_indices_segments_version_map_memory_in_bytes
    - elasticsearch_indices_shared_docs
    - elasticsearch_indices_store_size_bytes
    - elasticsearch_indices_store_size_bytes_primary
    - elasticsearch_indices_store_size_bytes_total
    - elasticsearch_indices_store_throttle_time_seconds_total
    - elasticsearch_indices_translog_operations
    - elasticsearch_indices_translog_size_in_bytes
    - elasticsearch_jvm_gc_collection_seconds_count
    - elasticsearch_jvm_gc_collection_seconds_sum
    - elasticsearch_jvm_memory_max_bytes
    - elasticsearch_jvm_memory_pool_max_bytes
    - elasticsearch_jvm_memory_used_bytes
    - elasticsearch_nodes_roles
    - elasticsearch_os_cpu_percent
    - elasticsearch_os_load1
    - elasticsearch_os_load15
    - elasticsearch_os_load5
    - elasticsearch_process_cpu_percent
    - elasticsearch_process_max_files_descriptors
    - elasticsearch_process_open_files_count
    - elasticsearch_thread_pool_active_count
    - elasticsearch_thread_pool_completed_count
    - elasticsearch_thread_pool_largest_count
    - elasticsearch_thread_pool_queue_count
    - elasticsearch_thread_pool_rejected_count
    - elasticsearch_thread_pool_threads_count
    - elasticsearch_transport_rx_size_bytes_total
    - elasticsearch_transport_tx_size_bytes_total

kubernetes-service-endpoints:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - http_request_duration_seconds_bucket
    - kube_cronjob_info
    - kube_cronjob_spec_suspend
    - kube_cronjob_status_active
    - kube_cronjob_status_last_schedule_time
    - kube_deployment_labels
    - kube_deployment_spec_replicas
    - kube_deployment_status_replicas_available
    - kube_horizontalpodautoscaler_spec_max_replicas
    - kube_horizontalpodautoscaler_spec_min_replicas
    - kube_horizontalpodautoscaler_status_current_replicas
    - kube_job_complete
    - kube_job_failed
    - kube_job_owner
    - kube_job_status_completion_time
    - kube_job_status_active
    - kube_job_status_succeeded
    - kube_job_status_start_time
    - kube_namespace_created
    - kube_node_info
    - kube_node_labels
    - kube_node_status_condition
    - kube_persistentvolumeclaim_info
    - kube_persistentvolumeclaim_resource_requests_storage_bytes
    - kube_pod_container_info
    - kube_pod_container_resource_limits
    - kube_pod_container_resource_requests
    - kube_pod_container_status_last_terminated_reason
    - kube_pod_container_status_restarts_total
    - kube_pod_container_status_running
    - kube_pod_ips
    - kube_pod_owner
    - kube_pod_status_scheduled
    - kube_pod_created
    - kube_replicaset_owner
    - kube_statefulset_labels
    - kube_statefulset_replicas
    - kube_statefulset_status_replicas_ready
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - flux_resource_info
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used

proxy-vm:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - node_cpu_seconds_total
    - node_disk_io_time_seconds_total
    - node_disk_read_bytes_total
    - node_disk_read_time_seconds_total
    - node_disk_reads_completed_total
    - node_disk_write_time_seconds_total
    - node_disk_writes_completed_total
    - node_disk_written_bytes_total
    - node_exporter_build_info
    - node_filesystem_avail_bytes
    - node_filesystem_size_bytes
    - node_load1
    - node_memory_Buffers_bytes
    - node_memory_Cached_bytes
    - node_memory_MemAvailable_bytes
    - node_memory_MemFree_bytes
    - node_memory_MemTotal_bytes
    - node_network_receive_bytes_total
    - node_network_receive_packets_total
    - node_network_transmit_bytes_total
    - node_nf_conntrack_entries
    - node_nf_conntrack_entries_limit
    - node_systemd_unit_state
    - node_uname_info
    - node_vmstat_pgpgin
    - node_vmstat_pgpgout
    - node_vmstat_pswpin
    - node_vmstat_pswpout
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - stackdriver_uptime_url_monitoring_googleapis_com_uptime_check_check_passed
